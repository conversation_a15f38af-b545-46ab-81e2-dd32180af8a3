<Project Sdk="Microsoft.NET.Sdk.Web">

  <ItemGroup>
    <ProjectReference Include="..\WTO.IdbSubmissions.Application\WTO.IdbSubmissions.Application.csproj" />
    <ProjectReference Include="..\WTO.IdbSubmissions.Infrastructure\WTO.IdbSubmissions.Infrastructure.csproj" />
    <ProjectReference Include="..\WTO.IdbSubmissions.Persistence\WTO.IdbSubmissions.Persistence.csproj" />
  </ItemGroup>

  <ItemGroup>
    <PackageReference Include="FastEndpoints" Version="6.1.0" />
    <PackageReference Include="Swashbuckle.AspNetCore" Version="7.2.0" />
  </ItemGroup>

  <ItemGroup>
    <Folder Include="Helpers\" />
  </ItemGroup>

  <PropertyGroup>
    <TargetFramework>net9.0</TargetFramework>
    <Nullable>enable</Nullable>
    <ImplicitUsings>enable</ImplicitUsings>
  </PropertyGroup>

</Project>
