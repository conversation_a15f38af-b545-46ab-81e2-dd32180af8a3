<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <TargetFramework>net9.0</TargetFramework>
    <ImplicitUsings>enable</ImplicitUsings>
    <Nullable>enable</Nullable>
    <IsPackable>false</IsPackable>
    <IsTestProject>true</IsTestProject>
  </PropertyGroup>

  <ItemGroup>
    <PackageReference Include="coverlet.collector" Version="6.0.2" />
    <PackageReference Include="Microsoft.NET.Test.Sdk" Version="17.12.0" />
    <PackageReference Include="xunit" Version="2.9.2" />
    <PackageReference Include="xunit.runner.visualstudio" Version="2.8.2" />
    <PackageReference Include="FluentAssertions" Version="6.12.0" />
    <PackageReference Include="Microsoft.AspNetCore.Mvc.Testing" Version="9.0.0" />
    <PackageReference Include="FastEndpoints" Version="6.1.0" />
    <PackageReference Include="FastEndpoints.Testing" Version="6.1.0" />
  </ItemGroup>

  <ItemGroup>
    <ProjectReference Include="..\..\WTO.IdbSubmissions.Web\WTO.IdbSubmissions.Web.csproj" />
    <ProjectReference Include="..\..\WTO.IdbSubmissions.Application\WTO.IdbSubmissions.Application.csproj" />
    <ProjectReference Include="..\..\WTO.IdbSubmissions.Domain\WTO.IdbSubmissions.Domain.csproj" />
    <ProjectReference Include="..\..\WTO.IdbSubmissions.Infrastructure\WTO.IdbSubmissions.Infrastructure.csproj" />
  </ItemGroup>

  <ItemGroup>
    <Using Include="Xunit" />
    <Using Include="FluentAssertions" />
    <Using Include="Microsoft.AspNetCore.Mvc.Testing" />
    <Using Include="FastEndpoints" />
    <Using Include="FastEndpoints.Testing" />
  </ItemGroup>

</Project>